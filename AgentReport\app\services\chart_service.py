"""Chart Service

This service handles chart generation from user queries using LLM analysis.
It processes natural language queries to create appropriate visualizations with real or mock data.
"""

import json
import logging
import random
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any, Union

from app.models.chart import (
    ChartType, ChartData, ChartDataPoint, ChartMetadata,
    ChartQueryRequest, ChartQueryResponse, ChartGenerationContext,
    ChartTypeRecommendation, MockDataGenerationConfig,
    DEFAULT_CHART_COLORS, CHART_TYPE_DESCRIPTIONS, CHART_SQL_REQUIREMENTS,
    PieChartData, BarChartData, LineChartData, NumberChartData,
    TableChartData, FunnelChartData, ActivityChartData, TextChartData,
    ImageChartData, DetailChartData
)
from app.models.database import Database, DatabaseType
from app.services.database_manager_service import DatabaseManagerService
from app.services.database_service import DatabaseService
from app.utils.bedrock_client import BedrockClient
from app.config import settings

logger = logging.getLogger(__name__)


class ChartService:
    """Service for generating charts from natural language queries using LLM analysis."""

    def __init__(self):
        """Initialize the chart service with required dependencies."""
        self.bedrock_client = BedrockClient()
        self.mock_config = MockDataGenerationConfig()
        self.database_manager = DatabaseManagerService()
        self.database_service = DatabaseService()

    async def process_chart_query(self, request: ChartQueryRequest) -> ChartQueryResponse:
        """
        Process a chart query request and return a complete chart response.

        Args:
            request: The chart query request from the frontend

        Returns:
            ChartQueryResponse with chart data or error information
        """
        try:
            logger.info(f"Processing chart query: {request.prompt[:100]}...")

            # Create generation context
            context = ChartGenerationContext(
                user_query=request.prompt,
                user_id=request.user_id,
                dashboard_id=request.dashboard_id,
                processing_notes=[]
            )

            # Analyze query and recommend chart type
            recommendation = await self._get_chart_type_recommendation(context)
            context.recommended_chart_type = recommendation.chart_type
            context.detected_intent = recommendation.reasoning

            # Generate data (real database data if database_id provided, otherwise mock data)
            if request.database_id and request.user_id:
                data_points, sql_query = await self._generate_real_chart_data(
                    context, recommendation.chart_type, request.database_id, request.user_id
                )
            else:
                data_points = await self._generate_mock_chart_data(context, recommendation.chart_type)
                sql_query = None

            # Create metadata and assemble final chart data
            metadata = self._create_chart_metadata(context, recommendation, sql_query)
            final_chart_type = context.recommended_chart_type

            # Convert structured data to legacy format for frontend compatibility
            legacy_data_points = self._convert_to_legacy_format(data_points, final_chart_type)

            chart_data = ChartData(
                title=recommendation.title,
                chartType=final_chart_type,
                data=legacy_data_points,
                metadata=metadata
            )

            logger.info(f"Successfully generated {recommendation.chart_type} chart: {recommendation.title}")
            return ChartQueryResponse(success=True, data=chart_data)

        except Exception as e:
            logger.error(f"Error processing chart query: {str(e)}")
            return ChartQueryResponse(
                success=False,
                error=f"Failed to generate chart: {str(e)}"
            )
    
    # ============================================================================
    # CHART TYPE RECOMMENDATION
    # ============================================================================

    async def _get_chart_type_recommendation(self, context: ChartGenerationContext) -> ChartTypeRecommendation:
        """
        Analyze the user query and recommend the most appropriate chart type.

        Args:
            context: Chart generation context

        Returns:
            ChartTypeRecommendation with the recommended chart type and reasoning
        """
        try:
            response = await self._get_llm_chart_recommendation(context)
            
            # Clean and extract JSON from response
            cleaned_response = self._extract_json_from_response(response)
            response_data = json.loads(cleaned_response)

            return ChartTypeRecommendation(
                chart_type=ChartType(response_data["chart_type"]),
                title=response_data.get("title", self._generate_fallback_title(context.user_query)),
                confidence=response_data["confidence"],
                reasoning=response_data["reasoning"],
                alternative_types=[ChartType(t) for t in response_data.get("alternative_types", [])]
            )

        except Exception as e:
            logger.warning(f"LLM chart type recommendation failed: {e}. Using fallback logic.")
            return self._fallback_chart_type_recommendation(context)

    async def _get_llm_chart_recommendation(self, context: ChartGenerationContext) -> str:
        """Get chart type recommendation and title from LLM."""
        system_prompt = """You are a data visualization expert. Analyze user queries and recommend the most appropriate chart type and generate a descriptive title.

Available chart types and their use cases:
- table: Detailed data display with multiple columns and precise values
- line: Showing trends and changes over continuous time periods
- timebar: Data changes over specific time intervals and periods
- bar: Comparing discrete categories, showing changes over distinct time periods
- funnel: Conversion rates, process stages, and sequential data
- number: Single key metrics, KPIs, and summary statistics
- image: Visual content, photos, graphics, or visual documentation
- detail: Comprehensive information views, drill-down data, and detailed analysis
- text: Descriptive information, summaries, explanations, and narrative content
- pie: Parts of a whole, percentage breakdowns, and composition analysis
- activity: Events, actions, timelines, and activity patterns over time

For the title, create a concise, descriptive chart title that clearly indicates what data is being visualized. Keep titles under 60 characters and make them professional and clear.

Respond with a JSON object containing:
{
    "chart_type": "recommended_type",
    "title": "Chart Title",
    "confidence": 0.95,
    "reasoning": "explanation of why this chart type is best",
    "alternative_types": ["alternative1", "alternative2"]
}"""

        user_prompt = f"""Analyze this user query and recommend the best chart type:

Query: "{context.user_query}"

Consider:
1. What type of data comparison or analysis is being requested?
2. Is this about trends over time, categorical comparisons, or composition?
3. Does the user want to see detailed data or high-level insights?
4. Are there keywords that suggest specific visualization needs?

Provide your recommendation as a JSON object."""

        return await self.bedrock_client.generate_response(
            prompt=user_prompt,
            system_prompt=system_prompt,
            temperature=0.3
        )

    def _extract_json_from_response(self, response: str) -> str:
        """Extract JSON object from LLM response that may contain extra content."""
        response = response.strip()
        
        # Try to find JSON object boundaries
        json_start = response.find('{')
        if json_start == -1:
            raise ValueError("No JSON object found in response")
        
        # Find the matching closing brace
        brace_count = 0
        json_end = -1
        
        for i in range(json_start, len(response)):
            if response[i] == '{':
                brace_count += 1
            elif response[i] == '}':
                brace_count -= 1
                if brace_count == 0:
                    json_end = i
                    break
        
        if json_end == -1:
            raise ValueError("No complete JSON object found in response")
        
        json_str = response[json_start:json_end + 1]
        logger.debug(f"Extracted JSON from LLM response: {json_str}")
        return json_str
    
    def _fallback_chart_type_recommendation(self, context: ChartGenerationContext) -> ChartTypeRecommendation:
        """
        Fallback chart type recommendation using keyword analysis.
        
        Args:
            context: Chart generation context
            
        Returns:
            ChartTypeRecommendation based on keyword analysis
        """
        query_lower = context.user_query.lower()
        
        # First check for explicit chart type requests
        if any(word in query_lower for word in ['pie chart', 'make it a pie', 'as a pie', 'pie graph']):
            chart_type = ChartType.PIE
            reasoning = "User explicitly requested a pie chart"
        elif any(word in query_lower for word in ['bar chart', 'make it a bar', 'as a bar', 'bar graph']):
            chart_type = ChartType.BAR
            reasoning = "User explicitly requested a bar chart"
        elif any(word in query_lower for word in ['line chart', 'make it a line', 'as a line', 'line graph']):
            chart_type = ChartType.LINE
            reasoning = "User explicitly requested a line chart"
        elif any(word in query_lower for word in ['table', 'make it a table', 'as a table']):
            chart_type = ChartType.TABLE
            reasoning = "User explicitly requested a table"
        elif any(word in query_lower for word in ['funnel chart', 'make it a funnel', 'as a funnel']):
            chart_type = ChartType.FUNNEL
            reasoning = "User explicitly requested a funnel chart"
        # Then check for content-based keywords
        elif any(word in query_lower for word in ['breakdown', 'distribution', 'percentage', 'share', 'composition']):
            chart_type = ChartType.PIE
            reasoning = "Query suggests composition or percentage analysis, pie chart is most appropriate"
        elif any(word in query_lower for word in ['over time', 'trend', 'timeline', 'monthly', 'daily', 'yearly']):
            chart_type = ChartType.LINE
            reasoning = "Query indicates time-based trend analysis, line chart is most appropriate"
        elif any(word in query_lower for word in ['time periods', 'time intervals', 'duration', 'schedule']):
            chart_type = ChartType.TIMEBAR
            reasoning = "Query suggests time interval analysis, time bar chart is most appropriate"
        elif any(word in query_lower for word in ['compare', 'comparison', 'vs', 'versus', 'between']):
            chart_type = ChartType.BAR
            reasoning = "Query suggests categorical comparison, bar chart is most appropriate"
        elif any(word in query_lower for word in ['detailed', 'list', 'rows', 'columns']):
            chart_type = ChartType.TABLE
            reasoning = "Query suggests detailed data display, table is most appropriate"
        elif any(word in query_lower for word in ['conversion', 'stages', 'process', 'steps']):
            chart_type = ChartType.FUNNEL
            reasoning = "Query suggests process or conversion analysis, funnel chart is most appropriate"
        elif any(word in query_lower for word in ['count', 'number', 'total', 'metric']) and len(query_lower.split()) < 5:
            chart_type = ChartType.NUMBER
            reasoning = "Query asks for a simple metric, number display is most appropriate"
        elif any(word in query_lower for word in ['activity', 'events', 'actions', 'history', 'log']):
            chart_type = ChartType.ACTIVITY
            reasoning = "Query suggests activity or event tracking, activity chart is most appropriate"
        elif any(word in query_lower for word in ['image', 'photo', 'picture', 'visual', 'graphic']):
            chart_type = ChartType.IMAGE
            reasoning = "Query suggests visual content display, image display is most appropriate"
        elif any(word in query_lower for word in ['detail', 'details', 'drill down', 'comprehensive', 'analysis']):
            chart_type = ChartType.DETAIL
            reasoning = "Query suggests detailed analysis, detail display is most appropriate"
        elif any(word in query_lower for word in ['text', 'description', 'summary', 'explanation', 'narrative']):
            chart_type = ChartType.TEXT
            reasoning = "Query suggests text-based information, text display is most appropriate"
        else:
            chart_type = ChartType.BAR
            reasoning = "Default to bar chart for general data visualization"
        
        return ChartTypeRecommendation(
            chart_type=chart_type,
            title=self._generate_fallback_title(context.user_query),
            confidence=0.7,
            reasoning=reasoning,
            alternative_types=[ChartType.BAR, ChartType.LINE, ChartType.TABLE]
        )

    def _generate_fallback_title(self, user_query: str) -> str:
        """Generate a fallback title from the user query."""
        query_words = user_query.strip().split()

        # If query is short enough, use it directly
        if len(query_words) <= 8:
            title = user_query.strip()
            # Capitalize first letter if not already
            if title and not title[0].isupper():
                title = title[0].upper() + title[1:]
            return title

        # For longer queries, create a shortened version
        key_words = query_words[:6]
        title = ' '.join(key_words)

        # Add ellipsis if we truncated
        if len(query_words) > 6:
            title += "..."

        # Capitalize first letter
        if title and not title[0].isupper():
            title = title[0].upper() + title[1:]

        return title
    
    # ============================================================================
    # DATA GENERATION
    # ============================================================================

    async def _generate_real_chart_data(self, context: ChartGenerationContext, chart_type: ChartType, database_id: str, user_id: str) -> Tuple[List[ChartDataPoint], Optional[str]]:
        """
        Generate chart data by querying a real database.

        Args:
            context: Chart generation context
            chart_type: The type of chart to generate data for
            database_id: ID of the database to query
            user_id: ID of the user making the request

        Returns:
            Tuple of (List of chart data points, SQL query used)
        """
        try:
            # Get database schema and validate
            database_info = await self._get_database_info(user_id, database_id)
            if not database_info:
                return await self._generate_mock_chart_data(context, chart_type), None

            # Generate SQL query
            sql_query = await self._generate_sql_for_chart_with_schema(context.user_query, database_info, chart_type)
            if not sql_query:
                logger.warning("Failed to generate SQL query, falling back to mock data")
                return await self._generate_mock_chart_data(context, chart_type), None

            # Execute query and transform results
            query_result = await self._execute_database_query(database_id, user_id, sql_query)
            if query_result is None:
                return await self._generate_mock_chart_data(context, chart_type), None

            # Transform results to chart data points
            data_points = self._transform_query_result_to_structured_data(query_result, chart_type)

            # Post-process for single data points (convert to number chart if appropriate)
            self._post_process_single_data_point(context, data_points, chart_type)

            logger.info(f"Successfully generated chart data from database {database_id}")
            return data_points, sql_query

        except Exception as e:
            logger.error(f"Error generating real chart data: {e}")
            fallback_data = await self._generate_mock_chart_data(context, chart_type)
            return fallback_data, None

    async def _get_database_info(self, user_id: str, database_id: str) -> Optional[Dict[str, Any]]:
        """Get and validate database information."""
        try:
            schema_info = await self.database_manager.get_database_schema(user_id)
            logger.info(f"Retrieved schema info for user {user_id}: {len(schema_info.get('databases', []))} databases")

            if not schema_info.get("databases"):
                logger.info(f"User {user_id} has no connected databases")
                return None

            # Find the specific database in the schema
            for db in schema_info.get("databases", []):
                if db["id"] == database_id:
                    if not db.get("tables"):
                        logger.warning(f"Database {database_id} has no tables")
                        return None
                    logger.info(f"Found database {database_id} with {len(db.get('tables', []))} tables")
                    return db

            logger.warning(f"Database {database_id} not found for user {user_id}")
            return None

        except Exception as e:
            logger.error(f"Failed to get database schema for user {user_id}: {e}")
            return None

    async def _execute_database_query(self, database_id: str, user_id: str, sql_query: str) -> Any:
        """Execute SQL query on the database."""
        try:
            # Get database credentials
            database_obj = await self.database_manager.credential_service.get_credentials(user_id, database_id)
            if not database_obj:
                logger.error(f"Database {database_id} not found for user {user_id}")
                return None

            # Establish connection
            success, error = await self.database_service.connect_database(database_obj, user_id=user_id)
            if not success:
                logger.error(f"Failed to connect to database {database_id}: {error}")
                return None

            # Execute query
            return await self.database_service.execute_query(database_id, sql_query)

        except Exception as e:
            logger.error(f"Failed to execute SQL query on database {database_id}: {e}")
            return None

    def _post_process_single_data_point(self, context: ChartGenerationContext, data_points: Any, chart_type: ChartType) -> None:
        """Post-process single data points for better UX."""
        # Check if we have structured data that represents a single value
        should_convert = False
        
        if isinstance(data_points, BarChartData) and len(data_points.categories) == 1:
            should_convert = True
        elif isinstance(data_points, PieChartData) and len(data_points.categories) == 1:
            should_convert = True
        elif isinstance(data_points, LineChartData) and len(data_points.time_periods) == 1:
            should_convert = True
        elif isinstance(data_points, list) and len(data_points) == 1:
            should_convert = self._should_convert_to_number_chart(context.user_query, data_points[0])
        
        if (should_convert and
            chart_type not in [ChartType.NUMBER, ChartType.TABLE, ChartType.TEXT, ChartType.IMAGE, ChartType.DETAIL]):
            
            logger.info(f"Converting single data point from {chart_type} to NUMBER chart for better UX")
            context.recommended_chart_type = ChartType.NUMBER

    # ============================================================================
    # MOCK DATA GENERATION
    # ============================================================================

    async def _generate_mock_chart_data(self, context: ChartGenerationContext, chart_type: ChartType) -> Union[
        PieChartData, BarChartData, LineChartData, NumberChartData,
        TableChartData, FunnelChartData, ActivityChartData, TextChartData,
        ImageChartData, DetailChartData, List[ChartDataPoint]
    ]:
        """
        Generate appropriate mock data for the chart type in structured format.

        Args:
            context: Chart generation context
            chart_type: The type of chart to generate data for

        Returns:
            Structured mock data in the appropriate format for the chart type
        """
        try:
            if chart_type == ChartType.PIE:
                return self._generate_mock_pie_data(context)
            elif chart_type == ChartType.LINE:
                return self._generate_mock_line_data(context)
            elif chart_type == ChartType.BAR:
                return self._generate_mock_bar_data(context)
            elif chart_type == ChartType.TIMEBAR:
                return self._generate_mock_timebar_data(context)
            elif chart_type == ChartType.FUNNEL:
                return self._generate_mock_funnel_data(context)
            elif chart_type == ChartType.NUMBER:
                return self._generate_mock_number_data(context)
            elif chart_type == ChartType.TABLE:
                return self._generate_mock_table_data(context)
            elif chart_type == ChartType.ACTIVITY:
                return self._generate_mock_activity_data(context)
            elif chart_type == ChartType.IMAGE:
                return self._generate_mock_image_data(context)
            elif chart_type == ChartType.DETAIL:
                return self._generate_mock_detail_data(context)
            elif chart_type == ChartType.TEXT:
                return self._generate_mock_text_data(context)
            else:
                # Default to bar chart data
                return self._generate_mock_bar_data(context)
        except Exception as e:
            logger.error(f"Error generating mock chart data: {e}")
            return self._generate_fallback_structured_data(chart_type)

    def _generate_mock_pie_data(self, context: ChartGenerationContext) -> PieChartData:
        """Generate mock data for pie charts."""
        categories = self._get_relevant_categories(context.user_query)
        values = []
        total = 100
        
        for i, category in enumerate(categories):
            if i == len(categories) - 1:
                # Last category gets remaining percentage
                values.append(total)
            else:
                value = random.randint(10, 40)
                values.append(value)
                total -= value
        
        return PieChartData(
            categories=categories,
            values=values,
            colors=DEFAULT_CHART_COLORS[:len(categories)]
        )

    def _generate_mock_line_data(self, context: ChartGenerationContext) -> LineChartData:
        """Generate mock data for line charts."""
        time_periods = self._get_time_labels(context.user_query)
        base_value = random.randint(100, 1000)
        values = []
        
        for i in range(len(time_periods)):
            # Add some trend and randomness
            trend = i * random.randint(-10, 20)
            noise = random.randint(-50, 50)
            value = max(0, base_value + trend + noise)
            values.append(value)
        
        return LineChartData(time_periods=time_periods, values=values)

    def _generate_mock_bar_data(self, context: ChartGenerationContext) -> BarChartData:
        """Generate mock data for bar charts."""
        categories = self._get_relevant_categories(context.user_query)
        values = [random.randint(50, 500) for _ in categories]
        
        return BarChartData(categories=categories, values=values)

    def _generate_mock_timebar_data(self, context: ChartGenerationContext) -> BarChartData:
        """Generate mock data for time bar charts."""
        time_periods = self._get_time_labels(context.user_query)
        values = [random.randint(50, 300) for _ in time_periods]
        
        return BarChartData(categories=time_periods, values=values)

    def _generate_mock_funnel_data(self, context: ChartGenerationContext) -> FunnelChartData:
        """Generate mock data for funnel charts."""
        stages = ["Initial", "Interested", "Qualified", "Proposal", "Closed"]
        initial_value = random.randint(1000, 5000)
        values = []
        
        current_value = initial_value
        for _ in stages:
            values.append(current_value)
            # Each stage has some drop-off
            current_value = int(current_value * random.uniform(0.6, 0.9))
        
        return FunnelChartData(stages=stages, values=values)

    def _generate_mock_number_data(self, context: ChartGenerationContext) -> NumberChartData:
        """Generate mock data for number displays."""
        value = random.randint(100, 10000)
        query_lower = context.user_query.lower()
        
        # Determine format type and label based on query
        if any(word in query_lower for word in ['revenue', 'sales', 'amount', 'cost', 'price']):
            format_type = "currency"
            label = "Total Revenue"
        elif any(word in query_lower for word in ['percent', 'rate', '%']):
            format_type = "percentage"
            label = "Success Rate"
            value = random.randint(10, 100)
        else:
            format_type = "number"
            label = "Total Count"
        
        return NumberChartData(value=value, label=label, format_type=format_type)

    def _generate_mock_table_data(self, context: ChartGenerationContext) -> TableChartData:
        """Generate mock data for table displays."""
        categories = self._get_relevant_categories(context.user_query)
        headers = ["Category", "Value", "Status"]
        rows = []
        
        for category in categories:
            value = random.randint(50, 500)
            status = random.choice(["Active", "Pending", "Completed"])
            rows.append([category, str(value), status])
        
        return TableChartData(headers=headers, rows=rows)

    def _generate_mock_activity_data(self, context: ChartGenerationContext) -> ActivityChartData:
        """Generate mock data for activity charts."""
        time_periods = self._get_time_labels(context.user_query)
        activity_counts = [random.randint(5, 150) for _ in time_periods]
        
        return ActivityChartData(time_periods=time_periods, activity_counts=activity_counts)

    def _generate_mock_image_data(self, context: ChartGenerationContext) -> ImageChartData:
        """Generate mock data for image displays."""
        return ImageChartData(
            image_url="https://via.placeholder.com/400x300?text=Sample+Image",
            alt_text="Sample placeholder image",
            caption="This is a sample image for demonstration purposes"
        )

    def _generate_mock_detail_data(self, context: ChartGenerationContext) -> DetailChartData:
        """Generate mock data for detail displays."""
        categories = self._get_relevant_categories(context.user_query)
        sections = []
        
        for category in categories:
            section = {
                "Name": category,
                "Value": str(random.randint(50, 500)),
                "Status": random.choice(["Active", "Pending", "Completed"]),
                "Description": f"Detailed information about {category}"
            }
            sections.append(section)
        
        return DetailChartData(
            title="Detailed Analysis",
            sections=sections
        )

    def _generate_mock_text_data(self, context: ChartGenerationContext) -> TextChartData:
        """Generate mock data for text displays."""
        content = f"Analysis based on query: '{context.user_query}'\n\n"
        content += "This is sample text content that would typically contain "
        content += "narrative analysis, insights, or descriptive information "
        content += "related to the user's query."
        
        return TextChartData(content=content, format_type="plain")

    def _generate_fallback_structured_data(self, chart_type: ChartType) -> Union[
        PieChartData, BarChartData, LineChartData, NumberChartData,
        TableChartData, FunnelChartData, ActivityChartData, TextChartData,
        ImageChartData, DetailChartData, List[ChartDataPoint]
    ]:
        """Generate fallback structured data when query transformation fails."""
        if chart_type == ChartType.PIE:
            return PieChartData(categories=["No Data"], values=[100.0])
        elif chart_type == ChartType.BAR:
            return BarChartData(categories=["No Data"], values=[0])
        elif chart_type == ChartType.LINE:
            return LineChartData(time_periods=["No Data"], values=[0])
        elif chart_type == ChartType.NUMBER:
            return NumberChartData(value=0, label="No Data", format_type="number")
        elif chart_type == ChartType.TABLE:
            return TableChartData(headers=["Status"], rows=[["No Data Available"]])
        elif chart_type == ChartType.FUNNEL:
            return FunnelChartData(stages=["No Data"], values=[0])
        elif chart_type == ChartType.ACTIVITY:
            return ActivityChartData(time_periods=["No Data"], activity_counts=[0])
        elif chart_type == ChartType.TEXT:
            return TextChartData(content="No data available", format_type="plain")
        elif chart_type == ChartType.IMAGE:
            return ImageChartData(image_url="", alt_text="No image available")
        elif chart_type == ChartType.DETAIL:
            return DetailChartData(title="No Data", sections=[{"Status": "No data available"}])
        else:
            return [ChartDataPoint(label="No Data", value=0)]

    def _should_convert_to_number_chart(self, user_query: str, data_point: ChartDataPoint) -> bool:
        """
        Determine if a single data point should be displayed as a number chart instead of other chart types.

        Args:
            user_query: The original user query
            data_point: The single data point returned

        Returns:
            True if should convert to number chart, False otherwise
        """
        query_lower = user_query.lower()
        logger.info(f"Checking conversion for query: '{user_query}' with data point: {data_point}")

        # Keywords that suggest a single metric/number is expected
        number_keywords = [
            'total', 'count', 'sum', 'average', 'mean', 'median', 'max', 'min',
            'how many', 'how much', 'number of', 'amount of', 'size of',
            'revenue', 'profit', 'cost', 'price', 'value', 'score', 'rating',
            'percentage', 'percent', 'rate'
        ]

        # Check if query contains number-indicating keywords
        has_number_keywords = any(keyword in query_lower for keyword in number_keywords)
        logger.info(f"Query has number keywords: {has_number_keywords}")

        # Check if the data point label suggests it's a single metric
        label_lower = data_point.label.lower()
        single_metric_labels = ['total', 'count', 'sum', 'average', 'value', 'metric', 'score']
        has_metric_label = any(label in label_lower for label in single_metric_labels)
        logger.info(f"Data point label '{data_point.label}' has metric label: {has_metric_label}")

        # For now, let's be more aggressive and convert any single data point to number
        # This will help with the current case where "Show me users by role" returns one result
        result = has_number_keywords or has_metric_label or True  # Always convert single points for now
        logger.info(f"Final conversion decision: {result}")

        return result

    # ============================================================================
    # UTILITY METHODS
    # ============================================================================

    def _get_relevant_categories(self, query: str) -> List[str]:
        """
        Extract or generate relevant categories based on the query.

        Args:
            query: User query string

        Returns:
            List of category labels
        """
        query_lower = query.lower()

        # Predefined category sets based on common query patterns
        if any(word in query_lower for word in ['product', 'item', 'goods']):
            return ["Product A", "Product B", "Product C", "Product D"]
        elif any(word in query_lower for word in ['department', 'team', 'division']):
            return ["Sales", "Marketing", "Engineering", "Support"]
        elif any(word in query_lower for word in ['region', 'location', 'geography']):
            return ["North", "South", "East", "West"]
        elif any(word in query_lower for word in ['channel', 'source', 'medium']):
            return ["Online", "Retail", "Partner", "Direct"]
        elif any(word in query_lower for word in ['age', 'demographic']):
            return ["18-25", "26-35", "36-45", "46+"]
        elif any(word in query_lower for word in ['priority', 'level', 'tier']):
            return ["High", "Medium", "Low"]
        else:
            return ["Category 1", "Category 2", "Category 3", "Category 4"]

    def _get_time_labels(self, query: str) -> List[str]:
        """
        Generate appropriate time labels based on the query.

        Args:
            query: User query string

        Returns:
            List of time period labels
        """
        query_lower = query.lower()

        if any(word in query_lower for word in ['daily', 'day', 'days']):
            base_date = datetime.now() - timedelta(days=6)
            return [(base_date + timedelta(days=i)).strftime("%m/%d") for i in range(7)]
        elif any(word in query_lower for word in ['weekly', 'week', 'weeks']):
            return [f"Week {i+1}" for i in range(8)]
        elif any(word in query_lower for word in ['monthly', 'month', 'months']):
            months = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"]
            return months[:6]  # Last 6 months
        elif any(word in query_lower for word in ['yearly', 'year', 'years', 'annual']):
            current_year = datetime.now().year
            return [str(current_year - i) for i in range(4, -1, -1)]  # Last 5 years
        elif any(word in query_lower for word in ['quarterly', 'quarter', 'quarters']):
            return ["Q1", "Q2", "Q3", "Q4"]
        else:
            # Default to months
            return ["Jan", "Feb", "Mar", "Apr", "May", "Jun"]

    def _create_chart_metadata(self, context: ChartGenerationContext, recommendation: ChartTypeRecommendation, sql_query: Optional[str] = None) -> ChartMetadata:
        """
        Create chart metadata based on context and recommendation.

        Args:
            context: Chart generation context
            recommendation: Chart type recommendation
            sql_query: Optional SQL query used to generate the data

        Returns:
            ChartMetadata object
        """
        # Generate axis labels based on chart type and query
        x_axis_label = self._generate_axis_label(context.user_query, "x", recommendation.chart_type)
        y_axis_label = self._generate_axis_label(context.user_query, "y", recommendation.chart_type)

        # Determine data source
        data_source = "Real Database Query" if sql_query else "Mock Data Generator"

        return ChartMetadata(
            xAxisLabel=x_axis_label,
            yAxisLabel=y_axis_label,
            colors=DEFAULT_CHART_COLORS[:6],  # Use first 6 colors
            sqlQuery=sql_query,
            description=recommendation.reasoning,
            dataSource=data_source,
            generatedAt=datetime.now().isoformat()
        )

    def _generate_axis_label(self, query: str, axis: str, chart_type: ChartType) -> Optional[str]:
        """
        Generate appropriate axis labels based on query and chart type.

        Args:
            query: User query
            axis: "x" or "y"
            chart_type: Type of chart

        Returns:
            Axis label or None
        """
        query_lower = query.lower()

        if chart_type in [ChartType.PIE, ChartType.NUMBER, ChartType.TEXT, ChartType.IMAGE, ChartType.DETAIL, ChartType.TABLE]:
            return None  # These chart types don't use axis labels

        if axis == "x":
            if any(word in query_lower for word in ['time', 'date', 'month', 'year', 'day']):
                return "Time Period"
            elif any(word in query_lower for word in ['category', 'type', 'group']):
                return "Category"
            else:
                return "Categories"
        else:  # y-axis
            if any(word in query_lower for word in ['count', 'number', 'total']):
                return "Count"
            elif any(word in query_lower for word in ['revenue', 'sales', 'amount', 'value']):
                return "Value"
            elif any(word in query_lower for word in ['percentage', 'percent', '%']):
                return "Percentage"
            else:
                return "Value"

    # ============================================================================
    # SQL GENERATION AND DATABASE OPERATIONS
    # ============================================================================

    async def _generate_sql_for_chart_with_schema(self, user_query: str, database_info: Dict[str, Any], chart_type: ChartType) -> Optional[str]:
        """
        Generate SQL query for chart data using LLM with actual database schema.

        Args:
            user_query: User's natural language query
            database_info: Database information including tables and columns
            chart_type: Type of chart being generated

        Returns:
            SQL query string or None if generation fails
        """
        try:
            # Build schema context from the database info
            schema_context = self._build_schema_context(database_info)

            # Get chart-specific SQL requirements
            chart_requirements = CHART_SQL_REQUIREMENTS.get(chart_type, CHART_SQL_REQUIREMENTS[ChartType.BAR])
            
            system_prompt = f"""You are a SQL expert. Generate a {database_info['type']} SQL query to answer the user's question.

Database Schema:
{schema_context}

Chart Type: {chart_type.value}
Required Output: {chart_requirements['description']}
Column Requirements: {chart_requirements['columns']} columns
Additional Requirements:
{chr(10).join(f"- {req}" for req in chart_requirements['requirements'])}

CRITICAL SQL Requirements:
1. Return ONLY the SQL query, no explanations
2. Use proper {database_info['type']} syntax
3. MUST return exactly {chart_requirements['columns']} columns (unless specified as "2+" or "1-2")
4. Use aggregate functions (COUNT, SUM, AVG) when appropriate
5. Order results appropriately for the chart type
6. Handle NULL values appropriately
7. IMPORTANT: Do NOT use column aliases in WHERE, GROUP BY, or HAVING clauses
8. If using HAVING, reference the full expression, not the alias

CRITICAL: If you need HAVING clause, use the full expression:
- CORRECT: "HAVING SUM(amount) > 0"
- INCORRECT: "HAVING value > 0" (when value is an alias)

For {chart_type.value} charts specifically:
- Column 1: {chart_requirements['description'].split(',')[0].split('SELECT')[1].strip()}
- Expected result: meaningful data that can be visualized as a {chart_type.value} chart
"""

            user_prompt = f"User question: {user_query}\n\nGenerate the SQL query:"

            # Generate and clean SQL query
            response = await self.bedrock_client.generate_response(
                prompt=user_prompt,
                system_prompt=system_prompt,
                temperature=0.1
            )

            sql_query = self._clean_sql_response(response)

            if not self._validate_sql_query(sql_query):
                return None

            logger.info(f"Generated clean SQL query: {sql_query}")
            return sql_query

        except Exception as e:
            logger.error(f"Error generating SQL query with schema: {e}")
            return None

    def _build_schema_context(self, database_info: Dict[str, Any]) -> str:
        """Build a readable schema context from database info."""
        schema_lines = []
        schema_lines.append(f"Database: {database_info['name']} ({database_info['type']})")
        schema_lines.append("")

        for table in database_info.get('tables', []):
            table_name = table['name']
            columns = table.get('columns', [])

            schema_lines.append(f"Table: {table_name}")
            if columns:
                schema_lines.append("Columns:")
                for col in columns:
                    schema_lines.append(f"  - {col}")
            else:
                schema_lines.append("  (No column information available)")
            schema_lines.append("")

        return "\n".join(schema_lines)

    def _clean_sql_response(self, response: str) -> str:
        """Clean SQL response from LLM by removing markdown formatting."""
        sql_query = response.strip()

        # Remove markdown code blocks
        if sql_query.startswith('```sql'):
            sql_query = sql_query[6:]
        elif sql_query.startswith('```'):
            sql_query = sql_query[3:]

        if sql_query.endswith('```'):
            sql_query = sql_query[:-3]

        return sql_query.strip()

    def _validate_sql_query(self, sql_query: str) -> bool:
        """Validate that the SQL query looks correct."""
        if not sql_query or not sql_query.upper().startswith('SELECT'):
            logger.warning(f"Generated SQL doesn't look valid: {sql_query}")
            return False
        return True

    # ============================================================================
    # DATA TRANSFORMATION
    # ============================================================================

    def _transform_query_result_to_structured_data(self, query_result: Any, chart_type: ChartType) -> Union[
        PieChartData, BarChartData, LineChartData, NumberChartData,
        TableChartData, FunnelChartData, ActivityChartData, TextChartData,
        ImageChartData, DetailChartData, List[ChartDataPoint]
    ]:
        """
        Transform database query results into chart-specific structured data.

        Args:
            query_result: Result from database query (DataFrame or list of dicts)
            chart_type: Type of chart being generated

        Returns:
            Structured data in the appropriate format for the chart type
        """
        try:
            logger.debug(f"Transforming query result for {chart_type}: type={type(query_result)}")

            # Convert result to DataFrame if it's not already
            if hasattr(query_result, 'to_dict'):
                df = query_result
            elif isinstance(query_result, list) and query_result:
                df = pd.DataFrame(query_result)
            else:
                logger.warning(f"Query result is empty or invalid format: {type(query_result)}")
                return self._generate_fallback_structured_data(chart_type)

            if df.empty:
                logger.warning("Query returned no data")
                return self._generate_fallback_structured_data(chart_type)

            logger.debug(f"DataFrame columns: {df.columns.tolist()}")
            logger.debug(f"DataFrame shape: {df.shape}")

            # Transform based on chart type
            if chart_type == ChartType.PIE:
                return self._transform_to_pie_data(df)
            elif chart_type == ChartType.BAR:
                return self._transform_to_bar_data(df)
            elif chart_type == ChartType.LINE:
                return self._transform_to_line_data(df)
            elif chart_type == ChartType.TIMEBAR:
                return self._transform_to_timebar_data(df)
            elif chart_type == ChartType.NUMBER:
                return self._transform_to_number_data(df)
            elif chart_type == ChartType.TABLE:
                return self._transform_to_table_data(df)
            elif chart_type == ChartType.FUNNEL:
                return self._transform_to_funnel_data(df)
            elif chart_type == ChartType.ACTIVITY:
                return self._transform_to_activity_data(df)
            elif chart_type == ChartType.TEXT:
                return self._transform_to_text_data(df)
            elif chart_type == ChartType.IMAGE:
                return self._transform_to_image_data(df)
            elif chart_type == ChartType.DETAIL:
                return self._transform_to_detail_data(df)
            else:
                # Fallback to legacy format
                return self._transform_to_legacy_data_points(df)

        except Exception as e:
            logger.error(f"Error transforming query result to structured data: {e}")
            return self._generate_fallback_structured_data(chart_type)

    def _transform_to_pie_data(self, df) -> PieChartData:
        """Transform DataFrame to pie chart data."""
        columns = df.columns.tolist()
        if len(columns) < 2:
            raise ValueError("Pie charts require at least 2 columns")
        
        categories = [str(val) for val in df[columns[0]].tolist()]
        values = [float(val) for val in df[columns[1]].tolist()]
        
        # Convert to percentages
        total = sum(values)
        if total > 0:
            percentages = [(val / total) * 100 for val in values]
        else:
            percentages = values
            
        return PieChartData(
            categories=categories,
            values=percentages,
            colors=DEFAULT_CHART_COLORS[:len(categories)]
        )

    def _transform_to_bar_data(self, df) -> BarChartData:
        """Transform DataFrame to bar chart data."""
        columns = df.columns.tolist()
        if len(columns) < 2:
            raise ValueError("Bar charts require at least 2 columns")
        
        categories = [str(val) for val in df[columns[0]].tolist()]
        values = [float(val) for val in df[columns[1]].tolist()]
        
        return BarChartData(categories=categories, values=values)

    def _transform_to_line_data(self, df) -> LineChartData:
        """Transform DataFrame to line chart data."""
        columns = df.columns.tolist()
        if len(columns) < 2:
            raise ValueError("Line charts require at least 2 columns")
        
        time_periods = [str(val) for val in df[columns[0]].tolist()]
        values = [float(val) for val in df[columns[1]].tolist()]
        
        return LineChartData(time_periods=time_periods, values=values)

    def _transform_to_timebar_data(self, df) -> BarChartData:
        """Transform DataFrame to time bar chart data (similar to bar chart)."""
        return self._transform_to_bar_data(df)

    def _transform_to_number_data(self, df) -> NumberChartData:
        """Transform DataFrame to number chart data."""
        # Get the first numeric value
        for col in df.columns:
            if df[col].dtype in ['int64', 'float64', 'int32', 'float32']:
                value = float(df[col].iloc[0])
                label = f"Total {col.replace('_', ' ').title()}"
                
                # Determine format type based on value and column name
                col_name_lower = col.lower()
                if any(word in col_name_lower for word in ['amount', 'price', 'cost', 'revenue', 'sales']):
                    format_type = "currency"
                elif any(word in col_name_lower for word in ['percent', 'rate', 'ratio']) or '%' in col_name_lower:
                    format_type = "percentage"
                else:
                    format_type = "number"
                    
                return NumberChartData(value=value, label=label, format_type=format_type)
        
        # Fallback
        return NumberChartData(value=0, label="No Data", format_type="number")

    def _transform_to_table_data(self, df) -> TableChartData:
        """Transform DataFrame to table chart data."""
        headers = [str(col).replace('_', ' ').title() for col in df.columns.tolist()]
        rows = []
        
        for _, row in df.iterrows():
            row_data = [str(val) if pd.notna(val) else "" for val in row.tolist()]
            rows.append(row_data)
        
        return TableChartData(headers=headers, rows=rows)

    def _transform_to_funnel_data(self, df) -> FunnelChartData:
        """Transform DataFrame to funnel chart data."""
        columns = df.columns.tolist()
        if len(columns) < 2:
            raise ValueError("Funnel charts require at least 2 columns")
        
        stages = [str(val) for val in df[columns[0]].tolist()]
        values = [float(val) for val in df[columns[1]].tolist()]
        
        return FunnelChartData(stages=stages, values=values)

    def _transform_to_activity_data(self, df) -> ActivityChartData:
        """Transform DataFrame to activity chart data."""
        columns = df.columns.tolist()
        if len(columns) < 2:
            raise ValueError("Activity charts require at least 2 columns")
        
        time_periods = [str(val) for val in df[columns[0]].tolist()]
        activity_counts = [int(float(val)) for val in df[columns[1]].tolist()]
        
        return ActivityChartData(time_periods=time_periods, activity_counts=activity_counts)

    def _transform_to_text_data(self, df) -> TextChartData:
        """Transform DataFrame to text chart data."""
        # Combine all text content
        content_parts = []
        for _, row in df.iterrows():
            row_text = " ".join([str(val) for val in row.tolist() if pd.notna(val)])
            content_parts.append(row_text)
        
        content = "\n".join(content_parts)
        return TextChartData(content=content, format_type="plain")

    def _transform_to_image_data(self, df) -> ImageChartData:
        """Transform DataFrame to image chart data."""
        columns = df.columns.tolist()
        image_url = str(df[columns[0]].iloc[0]) if not df.empty else ""
        caption = str(df[columns[1]].iloc[0]) if len(columns) > 1 and not df.empty else None
        
        return ImageChartData(
            image_url=image_url,
            alt_text=caption,
            caption=caption
        )

    def _transform_to_detail_data(self, df) -> DetailChartData:
        """Transform DataFrame to detail chart data."""
        sections = []
        
        for _, row in df.iterrows():
            section = {}
            for col, val in row.items():
                if pd.notna(val):
                    section[str(col).replace('_', ' ').title()] = str(val)
            sections.append(section)
        
        return DetailChartData(
            title="Detailed Information",
            sections=sections
        )

    def _transform_to_legacy_data_points(self, df) -> List[ChartDataPoint]:
        """Transform DataFrame to legacy ChartDataPoint format for backward compatibility."""
        columns = df.columns.tolist()
        data_points = []
        
        if len(columns) >= 2:
            label_col = columns[0]
            value_col = columns[1]
            
            for _, row in df.head(20).iterrows():
                try:
                    label = str(row[label_col])
                    value = float(row[value_col])
                    data_points.append(ChartDataPoint(label=label, value=value))
                except (ValueError, TypeError):
                    continue
        
        return data_points if data_points else [ChartDataPoint(label="No Data", value=0)]

    def _convert_to_legacy_format(self, data_points: Any, chart_type: ChartType) -> List[ChartDataPoint]:
        """
        Convert structured data objects to legacy ChartDataPoint format for frontend compatibility.

        Args:
            data_points: Structured data object (BarChartData, PieChartData, etc.) or legacy format
            chart_type: Type of chart

        Returns:
            List of ChartDataPoint objects that the frontend expects
        """
        try:
            # If it's already in legacy format, return as-is
            if isinstance(data_points, list):
                return data_points

            # Convert structured data to legacy format based on type
            if isinstance(data_points, BarChartData):
                return [ChartDataPoint(label=cat, value=val)
                       for cat, val in zip(data_points.categories, data_points.values)]

            elif isinstance(data_points, PieChartData):
                return [ChartDataPoint(label=cat, value=val)
                       for cat, val in zip(data_points.categories, data_points.values)]

            elif isinstance(data_points, LineChartData):
                return [ChartDataPoint(label=period, value=val)
                       for period, val in zip(data_points.time_periods, data_points.values)]

            elif isinstance(data_points, NumberChartData):
                return [ChartDataPoint(label=data_points.label, value=data_points.value)]

            elif isinstance(data_points, FunnelChartData):
                return [ChartDataPoint(label=stage, value=val)
                       for stage, val in zip(data_points.stages, data_points.values)]

            elif isinstance(data_points, ActivityChartData):
                return [ChartDataPoint(label=period, value=count)
                       for period, count in zip(data_points.time_periods, data_points.activity_counts)]

            elif isinstance(data_points, TableChartData):
                # For tables, create a simplified representation
                if data_points.rows:
                    return [ChartDataPoint(label=f"Row {i+1}", value=len(row))
                           for i, row in enumerate(data_points.rows[:10])]  # Limit to 10 rows
                else:
                    return [ChartDataPoint(label="No Data", value=0)]

            elif isinstance(data_points, TextChartData):
                # For text, create a single data point
                return [ChartDataPoint(label="Text Content", value=len(data_points.content))]

            elif isinstance(data_points, ImageChartData):
                # For images, create a single data point
                return [ChartDataPoint(label="Image", value=1)]

            elif isinstance(data_points, DetailChartData):
                # For details, create data points from sections
                if data_points.sections:
                    return [ChartDataPoint(label=f"Section {i+1}", value=len(section))
                           for i, section in enumerate(data_points.sections[:10])]
                else:
                    return [ChartDataPoint(label="No Data", value=0)]

            else:
                logger.warning(f"Unknown data type for conversion: {type(data_points)}")
                return [ChartDataPoint(label="Unknown Data", value=0)]

        except Exception as e:
            logger.error(f"Error converting to legacy format: {e}")
            return [ChartDataPoint(label="Error", value=0)]
